// HYBRID ARCHITECTURE: Organization Service
const { CENTRAL_WALLET_ID } = require('../../shared/constants');
// This service handles MVT transfers to organizations using:
// - Local MVT operations (deducting from user balance)
// - Organization balance management in DynamoDB
// - Member validation through Membership table
// - Complete audit trail for organization transfers

const { AWS, ddb, dynamoClient } = require('../../config/aws');
const { getTableName, tableExists } = require('../../shared/database/dynamoUtils');
const { TRANSACTION_TYPES, TRANSACTION_STATUS, TOKEN_TYPES } = require('../../shared/constants');
const { createDatabaseLogger, logError } = require('../../shared/utils/logger');
const { generateTransactionId } = require('../transaction/transaction.service');
const validationUtils = require('../../shared/utils/validationUtils');
const walletService = require('../wallet/wallet.service');


/**
 * Get organization details by organizationId
 * @param {string} organizationId - Organization ID
 * @returns {Promise<object>} - Organization details
 */
async function getOrganizationById(organizationId) {
  const logger = createDatabaseLogger({}, 'getOrganizationById', 'Organizations', { organizationId });
  try {
    logger.info({ organizationId }, 'Starting organization lookup');

    // Check if Organizations table exists
    const organizationsTableName = getTableName('Organizations');
    const organizationsTableExists = await tableExists(ddb, organizationsTableName);
    if (!organizationsTableExists) {
      throw new Error("Organizations table is not ready yet. Please try again after the schema deployment is complete.");
    }

    // Query Organizations table to get organization details
    const organizationParams = {
      TableName: organizationsTableName,
      Key: { id: organizationId }
    };
    console.log('organizationParams: ', organizationParams);

    const organizationResult = await dynamoClient.get(organizationParams).promise();
    if (!organizationResult.Item) {
      throw new Error(`Organization with ID ${organizationId} not found`);
    }

    const organization = organizationResult.Item;
    // Validate organization is active
    if (!organization.isActive) {
      throw new Error(`Organization ${organization.name} is not active`);
    }

    logger.info({
      organizationId: organization.id,
      organizationName: organization.name
    }, 'Organization validation successful');

    return {
      organization: organization,
      organizationId: organization.id,
      organizationName: organization.name
    };
  } catch (error) {
    logError(logger, error, 'getOrganizationById', { organizationId });
    throw error;
  }
}

/**
 * Get or create organization balance
 * @param {string} organizationId - Organization ID
 * @returns {Promise<object>} - Organization balance details
 */
async function getOrCreateOrganizationBalance(organizationId) {
  const logger = createDatabaseLogger({}, 'getOrCreateOrganizationBalance', 'OrganizationMVTBalance', { organizationId });
  
  try {
    // Check if OrganizationMVTBalance table exists
    const balanceTableName = getTableName('OrganizationMVTBalance');
    const balanceTableExists = await tableExists(ddb,balanceTableName);
    
    if (!balanceTableExists) {
      throw new Error("OrganizationMVTBalance table is not ready yet. Please try again after the schema deployment is complete.");
    }

    const organizationWalletId = `organization-wallet-${organizationId}`;
    
    // Try to get existing balance
    const getParams = {
      TableName: balanceTableName,
      Key: { id: organizationWalletId }
    };

    const result = await dynamoClient.get(getParams).promise();
    
    if (result.Item) {
      logger.info({ organizationId, balance: result.Item.balance }, 'Found existing organization balance');
      return result.Item;
    }

    // Create new organization balance if it doesn't exist
    const now = new Date().toISOString();
    const newBalance = {
      id: organizationWalletId,
      organizationId: organizationId,
      balance: 0,
      pendingBalance: 0,
      lockedBalance: 0,
      lastUpdated: now,
      totalReceived: 0,
      totalSent: 0,
      isDeleted: "false",
      createdAt: now,
      updatedAt: now
    };

    const putParams = {
      TableName: balanceTableName,
      Item: newBalance,
      ConditionExpression: 'attribute_not_exists(id)' // Prevent overwriting existing balance
    };

    await dynamoClient.put(putParams).promise();
    
    logger.info({ organizationId, organizationWalletId }, 'Created new organization balance');
    
    return newBalance;

  } catch (error) {
    if (error.code === 'ConditionalCheckFailedException') {
      // Balance was created by another concurrent request, fetch it
      const getParams = {
        TableName: getTableName('OrganizationMVTBalance'),
        Key: { id: `organization-wallet-${organizationId}` }
      };
      const result = await dynamoClient.get(getParams).promise();
      return result.Item;
    }
    
    logError(logger, error, 'getOrCreateOrganizationBalance', { organizationId });
    throw error;
  }
}

/**
 * Update organization balance
 * @param {string} organizationId - Organization ID
 * @param {number} newBalance - New balance amount
 * @param {number} totalReceived - Total received amount
 * @param {number} totalSent - Total sent amount
 * @returns {Promise<object>} - Updated balance details
 */
async function updateOrganizationBalance(organizationId, newBalance, totalReceived, totalSent) {
  const logger = createDatabaseLogger({}, 'updateOrganizationBalance', 'OrganizationMVTBalance', { 
    organizationId, newBalance, totalReceived, totalSent 
  });
  
  try {
    const balanceTableName = getTableName('OrganizationMVTBalance');
    const organizationWalletId = `organization-wallet-${organizationId}`;
    const now = new Date().toISOString();

    const updateParams = {
      TableName: balanceTableName,
      Key: { id: organizationWalletId },
      UpdateExpression: 'SET balance = :balance, totalReceived = :totalReceived, totalSent = :totalSent, lastUpdated = :lastUpdated, updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':balance': newBalance,
        ':totalReceived': totalReceived,
        ':totalSent': totalSent,
        ':lastUpdated': now,
        ':updatedAt': now
      },
      ReturnValues: 'ALL_NEW'
    };

    const result = await dynamoClient.update(updateParams).promise();
    
    logger.info({
      organizationId,
      newBalance,
      totalReceived,
      totalSent
    }, 'Organization balance updated successfully');
    
    return result.Attributes;

  } catch (error) {
    logError(logger, error, 'updateOrganizationBalance', { organizationId, newBalance, totalReceived, totalSent });
    throw error;
  }
}

/**
 * Transfer MVT tokens from user to organization
 * @param {string} fromUserId - Sender user ID
 * @param {string} memberId - Target organization member ID
 * @param {number} amount - Amount to transfer
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function transferMVTToOrganization(fromUserId, memberId, amount, description) {
  const logger = createDatabaseLogger({}, 'transferMVTToOrganization', 'MVTWalletTransaction', {
    fromUserId, memberId, amount
  });

  try {
    // Validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(amount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    logger.info({ fromUserId, memberId, amount }, 'Starting MVT transfer to organization');

    // 1. Validate member exists and get organization details
    const memberData = await getOrganizationById(memberId);
    const { organizationId, organizationName, memberName } = memberData;

    // 2. Get current sender balance
    const senderBalance = await walletService.getUserBalance(fromUserId);

    if (senderBalance.balance < amount) {
      throw new Error(
        `Insufficient balance. Available: ${senderBalance.balance}, Requested: ${amount}`
      );
    }

    // 3. Get or create organization balance
    const organizationBalance = await getOrCreateOrganizationBalance(organizationId);

    // 4. Calculate new balances
    const newSenderBalance = senderBalance.balance - amount;
    const newSenderTotalSent = senderBalance.totalSent + amount;
    const newOrganizationBalance = organizationBalance.balance + amount;
    const newOrganizationTotalReceived = organizationBalance.totalReceived + amount;

    // 5. Generate transaction ID and timestamp
    const now = new Date().toISOString();
    const transactionId = generateTransactionId('org-transfer');

    // 6. Update sender balance
    await walletService.updateUserBalance(
      fromUserId,
      newSenderBalance,
      senderBalance.totalReceived,
      newSenderTotalSent
    );

    // 7. Update organization balance
    await updateOrganizationBalance(
      organizationId,
      newOrganizationBalance,
      newOrganizationTotalReceived,
      organizationBalance.totalSent
    );

    // 8. Create transaction record
    const transactionData = await createMVTWalletTransaction({
      id: transactionId,
      transactionType: TRANSACTION_TYPES.CENTRAL_TO_ORGANIZATION_TRANSFER,
      tokenType: TOKEN_TYPES.MVT,
      amount: amount,
      fromWalletId: `user-wallet-${fromUserId}`,
      toWalletId: `organization-wallet-${organizationId}`,
      fromUserId: fromUserId,
      toUserId: null, // No specific user recipient for organization transfers
      organizationId: organizationId,
      memberId: memberId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `User transferred ${amount} MVT tokens to ${organizationName}`,
      adminUserId: null,
      metadata: {
        operation: "user_to_organization_transfer",
        fromUser: fromUserId,
        toOrganization: organizationId,
        organizationName: organizationName,
        memberId: memberId,
        memberName: memberName,
        newSenderBalance: newSenderBalance,
        newOrganizationBalance: newOrganizationBalance
      },
      createdAt: now,
      updatedAt: now,
    });

    logger.info({
      transactionId,
      fromUserId,
      organizationId,
      organizationName,
      amount,
      newSenderBalance,
      newOrganizationBalance
    }, 'MVT transfer to organization completed successfully');

    return transactionData;

  } catch (error) {
    logError(logger, error, 'transferMVTToOrganization', { fromUserId, memberId, amount });

    if (error.code === 'ResourceNotFoundException') {
      throw new Error("Database tables are not ready yet. Please try again after the schema deployment is complete.");
    }

    throw new Error(error.message || "Failed to transfer MVT tokens to organization");
  }
}

/**
 * Create MVT wallet transaction record
 * @param {object} transactionData - Transaction data object
 * @returns {Promise<object>} - Created transaction data
 */
async function createMVTWalletTransaction(transactionData) {
  const logger = createDatabaseLogger({}, 'createMVTWalletTransaction', 'MVTWalletTransaction', {
    transactionId: transactionData.id
  });

  try {
    const transactionTableName = getTableName('MVTWalletTransaction');
    const transactionTableExists = await tableExists(ddb,transactionTableName);

    if (!transactionTableExists) {
      throw new Error("MVTWalletTransaction table is not ready yet. Please try again after the schema deployment is complete.");
    }

    // Prepare transaction data with proper metadata serialization
    const transactionRecord = {
      ...transactionData,
      metadata: JSON.stringify(transactionData.metadata)
    };

    const putParams = {
      TableName: transactionTableName,
      Item: transactionRecord
    };

    await dynamoClient.put(putParams).promise();

    logger.info({ transactionId: transactionData.id }, 'Transaction record created successfully');

    // Return the original transaction data (not the serialized version)
    return transactionData;

  } catch (error) {
    logError(logger, error, 'createMVTWalletTransaction', { transactionId: transactionData.id });
    throw error;
  }
}

/**
 * Transfer MVT tokens from central wallet to organization (for activity rewards)
 * @param {string} memberId - Organization member ID
 * @param {number} amount - Amount to transfer
 * @param {string} description - Transaction description
 * @param {string} adminUserId - Admin user ID performing the transfer
 * @returns {Promise<object>} - Transaction data
 */
async function transferMVTFromCentralToOrganization(memberId, amount, description, adminUserId) {
  const logger = createDatabaseLogger({}, 'transferMVTFromCentralToOrganization', 'MVTWalletTransaction', {
    memberId, amount
  });

  try {
    // Validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(amount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    // Get organization details from member ID
    const memberData = await getOrganizationById(memberId);
    const { organizationId, organizationName } = memberData;

    logger.info({ organizationId, organizationName }, 'Organization identified for central transfer');

    // Get current central wallet balance
    const centralBalance = await walletService.getCentralWalletBalance();
    if (centralBalance.balance < amount) {
      throw new Error(`Insufficient central wallet balance. Available: ${centralBalance.balance}, Requested: ${amount}`);
    }

    // Get or create organization balance
    const orgBalance = await getOrCreateOrganizationBalance(organizationId);

    // Calculate new balances
    const newCentralBalance = centralBalance.balance - amount;
    const newOrgBalance = orgBalance.balance + amount;
    const newOrgTotalReceived = orgBalance.totalReceived + amount;

    // Generate transaction ID
    const transactionId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    // Update central wallet balance
    await walletService.updateCentralWalletBalance(
      newCentralBalance,
      centralBalance.totalMinted,
      centralBalance.totalTransferred + amount
    );

    // Update organization balance
    await updateOrganizationBalance(
      organizationId,
      newOrgBalance,
      newOrgTotalReceived,
      orgBalance.totalSent
    );

    // Create transaction record with organization reference
    const transactionData = {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.CENTRAL_TO_ORGANIZATION_TRANSFER,
      tokenType: TOKEN_TYPES.MVT,
      amount: amount,
      fromWalletId: CENTRAL_WALLET_ID,
      toWalletId: `organization-wallet-${organizationId}`,
      fromUserId: null,
      toUserId: null,
      organizationId: organizationId,
      memberId: memberId,
      toOrganizationId: organizationId,
      toOrganization: { id: organizationId, name: organizationName },
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `Central wallet transferred ${amount} MVT tokens to organization ${organizationName}`,
      adminUserId: adminUserId,
      isDeleted: 'false', // Explicitly set isDeleted to 'false' as string to match DynamoDB schema
      metadata: JSON.stringify({
        operation: "central_to_organization_transfer",
        organizationId: organizationId,
        organizationName: organizationName,
        memberId: memberId,
        newOrgBalance: newOrgBalance,
        newCentralBalance: newCentralBalance,
        activityReward: true
      }),
      createdAt: now,
      updatedAt: now,
    };

    await createMVTWalletTransaction(transactionData);

    logger.info({
      transactionId,
      organizationId,
      amount,
      newOrgBalance
    }, 'Central to organization transfer completed successfully');

    return transactionData;

  } catch (error) {
    logError(logger, error, 'transferMVTFromCentralToOrganization', { memberId, amount });

    if (error.code === 'ResourceNotFoundException') {
      throw new Error("Database tables are not ready yet. Please try again after the schema deployment is complete.");
    }

    throw new Error(error.message || "Failed to transfer MVT tokens from central wallet to organization");
  }
}

module.exports = {
  getOrganizationById,
  getOrCreateOrganizationBalance,
  updateOrganizationBalance,
  transferMVTToOrganization,
  transferMVTFromCentralToOrganization,
  createMVTWalletTransaction
};
