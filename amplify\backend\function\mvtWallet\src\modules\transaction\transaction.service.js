const { AWS, ddb } = require('../../config/aws');
const { getTableName, tableExists } = require('../../shared/database/dynamoUtils');
const { CENTRAL_WALLET_ID, TRANSACTION_TYPES, TRANSACTION_STATUS, TOKEN_TYPES } = require('../../shared/constants');
const walletService = require('../wallet/wallet.service');
const validationUtils = require('../../shared/utils/validationUtils');
const { createDatabaseLogger, logError } = require('../../shared/utils/logger');

/**
 * Generate a unique transaction ID
 * @param {string} prefix - Transaction prefix (e.g., 'mint', 'transfer')
 * @returns {string} - Unique transaction ID
 */
function generateTransactionId(prefix) {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Validate and sanitize wallet IDs to prevent null values
 * @param {string} walletId - Wallet ID to validate
 * @param {string} transactionType - Transaction type for context
 * @param {string} direction - 'from' or 'to' for context
 * @returns {string} - Valid wallet ID
 */
function validateWalletId(walletId, transactionType, direction) {
  if (walletId && typeof walletId === 'string' && walletId.trim() !== '') {
    return walletId.trim();
  }

  // Provide appropriate defaults based on transaction type and direction
  switch (transactionType) {
    case TRANSACTION_TYPES.ADMIN_MINT:
      return direction === 'from' ? 'system-mint-wallet' : CENTRAL_WALLET_ID;

    case TRANSACTION_TYPES.CENTRAL_TO_USER_TRANSFER:
      return direction === 'from' ? CENTRAL_WALLET_ID : 'user-wallet-unknown';

    case TRANSACTION_TYPES.USER_TO_USER_TRANSFER:
      return direction === 'from' ? 'user-wallet-unknown' : 'user-wallet-unknown';

    case TRANSACTION_TYPES.USDC_DEPOSIT:
      return direction === 'from' ? 'external-usdc-wallet' : 'usdc-liquidity-pool';

    case TRANSACTION_TYPES.USDC_WITHDRAWAL:
      return direction === 'from' ? 'usdc-liquidity-pool' : 'external-usdc-wallet';

    case TRANSACTION_TYPES.SWAP_APPROVED:
      if (direction === 'from') {
        return 'swap-source-wallet';
      } else {
        return 'swap-destination-wallet';
      }

    case TRANSACTION_TYPES.SWAP_REJECTED:
      return direction === 'from' ? 'user-wallet-unknown' : 'user-wallet-unknown';

    default:
      return direction === 'from' ? 'system-wallet' : 'system-wallet';
  }
}

/**
 * Create MVT Wallet Transaction record
 * @param {object} transactionData - Transaction data object
 * @returns {Promise<boolean>} - True if successful, false if table doesn't exist
 */
async function createMVTWalletTransaction(transactionData) {
  try {
    const transactionTableName = getTableName("MVTWalletTransaction");

    // Check if table exists first
    const exists = await tableExists(ddb, transactionTableName);
    if (!exists) {
      const logger = createDatabaseLogger({}, 'createMVTWalletTransaction', 'MVTWalletTransaction');
      logger.warn({ tableName: transactionTableName }, 'MVTWalletTransaction table not found, skipping transaction record');
      return false;
    }

    // Validate and sanitize transaction type before processing
    const validatedTransactionType = validateTransactionType(transactionData.transactionType);

    // Determine token type based on transaction type or explicit tokenType
    let tokenType = transactionData.tokenType || TOKEN_TYPES.MVT; // Use explicit tokenType if provided, default to MVT

    // Override based on transaction type if not explicitly set
    if (!transactionData.tokenType) {
      if (validatedTransactionType === TRANSACTION_TYPES.USDC_DEPOSIT ||
          validatedTransactionType === TRANSACTION_TYPES.USDC_WITHDRAWAL) {
        tokenType = TOKEN_TYPES.USDC;
      }
      // For SWAP_APPROVED transactions, tokenType should be explicitly set by caller
      // to distinguish between MVT deduction and USDC addition
    }

    // Validate and sanitize wallet IDs to prevent null values
    const validatedFromWalletId = validateWalletId(transactionData.fromWalletId, validatedTransactionType, 'from');
    const validatedToWalletId = validateWalletId(transactionData.toWalletId, validatedTransactionType, 'to');

    // Log wallet ID validation for debugging
    if (!transactionData.fromWalletId || !transactionData.toWalletId) {
      console.log(`Wallet ID validation applied for transaction ${transactionData.id}:`, {
        originalFromWalletId: transactionData.fromWalletId,
        originalToWalletId: transactionData.toWalletId,
        validatedFromWalletId,
        validatedToWalletId,
        transactionType: validatedTransactionType
      });
    }

    // Ensure all required fields are present with defaults
    const now = new Date().toISOString();
    const walletTransaction = {
      id: transactionData.id,
      transactionType: validatedTransactionType,
      tokenType: tokenType,
      amount: transactionData.amount,
      fromWalletId: validatedFromWalletId,
      toWalletId: validatedToWalletId,
      fromUserId: transactionData.fromUserId || null,
      toUserId: transactionData.toUserId || null,
      status: transactionData.status || TRANSACTION_STATUS.COMPLETED,
      transactionHash: transactionData.transactionHash,
      internalTxId: transactionData.internalTxId || transactionData.id,
      description: transactionData.description,
      adminUserId: transactionData.adminUserId || null,
      gasUsed: transactionData.gasUsed || null,
      gasPrice: transactionData.gasPrice || null,
      blockNumber: transactionData.blockNumber || null,
      confirmations: transactionData.confirmations || null,
      metadata: transactionData.metadata ? JSON.stringify(transactionData.metadata) : null,
      isDeleted: "false",
      createdAt: transactionData.createdAt || now,
      updatedAt: transactionData.updatedAt || now,
      __typename: "MVTWalletTransaction",
    };

    const createTransactionParams = {
      TableName: transactionTableName,
      Item: AWS.DynamoDB.Converter.marshall(walletTransaction),
    };

    await ddb.putItem(createTransactionParams).promise();
    return true;
  } catch (error) {
    const logger = createDatabaseLogger({}, 'createMVTWalletTransaction', 'MVTWalletTransaction');
    logError(logger, error, 'createMVTWalletTransaction');
    // Don't throw here - the main operation was successful even if we couldn't log it
    return false;
  }
}

/**
 * Mint MVT tokens to central wallet
 * @param {number} amount - Amount to mint
 * @param {string} description - Transaction description
 * @param {string} adminUserId - Admin user ID performing the mint
 * @returns {Promise<object>} - Transaction data
 */
async function mintMVTTokens(amount, description, adminUserId) {
  try {
    // Validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(amount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    const now = new Date().toISOString();
    const transactionId = generateTransactionId('mint');

    // Get current central wallet balance
    const currentWallet = await walletService.getCentralWalletBalance();
    const newBalance = currentWallet.balance + amount;
    const newTotalMinted = currentWallet.totalMinted + amount;

    // Update central wallet balance
    await walletService.updateCentralWalletBalance(
      newBalance,
      newTotalMinted,
      currentWallet.totalTransferred,
      null, // newTotalReceived - not applicable for minting
      now   // lastMintedAt - timestamp when minting occurred
    );

    // Create transaction record in MVTWalletTransaction table
    await createMVTWalletTransaction({
      id: transactionId,
      transactionType: TRANSACTION_TYPES.ADMIN_MINT,
      tokenType: TOKEN_TYPES.MVT, // Explicitly set token type
      amount: amount,
      fromWalletId: "system-mint-wallet",
      toWalletId: CENTRAL_WALLET_ID,
      fromUserId: null,
      toUserId: null,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `Admin minted ${amount} MVT tokens`,
      adminUserId: adminUserId,
      metadata: { operation: "mint", centralWallet: true },
      createdAt: now,
      updatedAt: now,
    });

    return {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.ADMIN_MINT,
      amount: amount,
      fromWalletId: "system-mint-wallet",
      toWalletId: CENTRAL_WALLET_ID,
      fromUserId: null,
      toUserId: null,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `Admin minted ${amount} MVT tokens`,
      adminUserId: adminUserId,
      gasUsed: null,
      gasPrice: null,
      blockNumber: null,
      confirmations: null,
      metadata: JSON.stringify({ operation: "mint", centralWallet: true }),
      createdAt: now,
      updatedAt: now,
    };
  } catch (error) {
    const logger = createDatabaseLogger({}, 'mintMVTTokens', 'MVTTokenWallet', { amount });
    logError(logger, error, 'mintMVTTokens', { amount });
    throw new Error("Failed to mint MVT tokens");
  }
}

/**
 * Transfer MVT tokens from central wallet to user
 * @param {string} userId - Target user ID
 * @param {number} amount - Amount to transfer
 * @param {string} description - Transaction description
 * @param {string} adminUserId - Admin user ID performing the transfer
 * @returns {Promise<object>} - Transaction data
 */
async function transferMVTToUser(userId, amount, description, adminUserId) {
  try {
    // Validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(amount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    const now = new Date().toISOString();
    const transactionId = generateTransactionId('transfer');

    // Get current central wallet balance
    const currentWallet = await walletService.getCentralWalletBalance();

    if (currentWallet.balance < amount) {
      throw new Error(
        `Insufficient balance in central wallet. Available: ${currentWallet.balance}, Requested: ${amount}`
      );
    }

    // Get current user balance
    const currentUserBalance = await walletService.getUserBalance(userId);

    // Calculate new balances
    const newCentralBalance = currentWallet.balance - amount;
    const newCentralTotalTransferred = currentWallet.totalTransferred + amount;
    const newUserBalance = currentUserBalance.balance + amount;
    const newUserTotalReceived = currentUserBalance.totalReceived + amount;

    // Update central wallet balance
    await walletService.updateCentralWalletBalance(
      newCentralBalance,
      currentWallet.totalMinted,
      newCentralTotalTransferred
    );

    // Update user balance (only if table exists)
    await walletService.updateUserBalance(userId, newUserBalance, newUserTotalReceived, currentUserBalance.totalSent);

    // Create transaction record in MVTWalletTransaction table
    await createMVTWalletTransaction({
      id: transactionId,
      transactionType: TRANSACTION_TYPES.CENTRAL_TO_USER_TRANSFER,
      tokenType: TOKEN_TYPES.MVT, // Explicitly set token type
      amount: amount,
      fromWalletId: CENTRAL_WALLET_ID,
      toWalletId: `user-wallet-${userId}`,
      fromUserId: null,
      toUserId: userId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `Admin transferred ${amount} MVT tokens to user`,
      adminUserId: adminUserId,
      metadata: {
        operation: "transfer",
        fromCentralWallet: true,
        toUser: userId,
        newUserBalance: newUserBalance,
        newCentralBalance: newCentralBalance
      },
      createdAt: now,
      updatedAt: now,
    });

    return {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.CENTRAL_TO_USER_TRANSFER,
      amount: amount,
      fromWalletId: CENTRAL_WALLET_ID,
      toWalletId: `user-wallet-${userId}`,
      fromUserId: null,
      toUserId: userId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `Admin transferred ${amount} MVT tokens to user`,
      adminUserId: adminUserId,
      gasUsed: null,
      gasPrice: null,
      blockNumber: null,
      confirmations: null,
      metadata: JSON.stringify({
        operation: "transfer",
        fromCentralWallet: true,
        toUser: userId,
        newUserBalance: newUserBalance,
        newCentralBalance: newCentralBalance
      }),
      createdAt: now,
      updatedAt: now,
    };
  } catch (error) {
    const logger = createDatabaseLogger({}, 'transferMVTTokensToUser', 'MVTTokenWallet', { userId, amount });
    logError(logger, error, 'transferMVTTokensToUser', { userId, amount });

    if (error.code === 'ResourceNotFoundException') {
      throw new Error("Database tables are not ready yet. Please try again after the schema deployment is complete.");
    }

    throw new Error(error.message || "Failed to transfer MVT tokens to user");
  }
}

/**
 * Transfer MVT tokens from one user to another user
 * @param {string} fromUserId - Sender user ID
 * @param {string} toUserId - Recipient user ID
 * @param {number} amount - Amount to transfer
 * @param {string} description - Transaction description
 * @returns {Promise<object>} - Transaction data
 */
async function transferMVTBetweenUsers(fromUserId, toUserId, amount, description) {
  try {
    // Validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(amount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    const now = new Date().toISOString();
    const transactionId = generateTransactionId('user-transfer');

    // Validate that users are different
    if (fromUserId === toUserId) {
      throw new Error("Cannot transfer tokens to yourself");
    }

    // Get current sender balance
    const senderBalance = await walletService.getUserBalance(fromUserId);

    if (senderBalance.balance < amount) {
      throw new Error(
        `Insufficient balance. Available: ${senderBalance.balance}, Requested: ${amount}`
      );
    }

    // Get current recipient balance
    const recipientBalance = await walletService.getUserBalance(toUserId);

    // Calculate new balances
    const newSenderBalance = senderBalance.balance - amount;
    const newSenderTotalSent = senderBalance.totalSent + amount;
    const newRecipientBalance = recipientBalance.balance + amount;
    const newRecipientTotalReceived = recipientBalance.totalReceived + amount;

    // Update sender balance
    await walletService.updateUserBalance(
      fromUserId,
      newSenderBalance,
      senderBalance.totalReceived,
      newSenderTotalSent
    );

    // Update recipient balance
    await walletService.updateUserBalance(
      toUserId,
      newRecipientBalance,
      newRecipientTotalReceived,
      recipientBalance.totalSent
    );

    // Create transaction record in MVTWalletTransaction table
    await createMVTWalletTransaction({
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USER_TO_USER_TRANSFER,
      tokenType: TOKEN_TYPES.MVT, // Explicitly set token type
      amount: amount,
      fromWalletId: `user-wallet-${fromUserId}`,
      toWalletId: `user-wallet-${toUserId}`,
      fromUserId: fromUserId,
      toUserId: toUserId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `User transferred ${amount} MVT tokens`,
      adminUserId: null,
      metadata: {
        operation: "user_to_user_transfer",
        fromUser: fromUserId,
        toUser: toUserId,
        newSenderBalance: newSenderBalance,
        newRecipientBalance: newRecipientBalance
      },
      createdAt: now,
      updatedAt: now,
    });

    return {
      id: transactionId,
      transactionType: TRANSACTION_TYPES.USER_TO_USER_TRANSFER,
      amount: amount,
      fromWalletId: `user-wallet-${fromUserId}`,
      toWalletId: `user-wallet-${toUserId}`,
      fromUserId: fromUserId,
      toUserId: toUserId,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: `internal-${transactionId}`,
      internalTxId: transactionId,
      description: description || `User transferred ${amount} MVT tokens`,
      adminUserId: null,
      gasUsed: null,
      gasPrice: null,
      blockNumber: null,
      confirmations: null,
      metadata: JSON.stringify({
        operation: "user_to_user_transfer",
        fromUser: fromUserId,
        toUser: toUserId,
        newSenderBalance: newSenderBalance,
        newRecipientBalance: newRecipientBalance
      }),
      createdAt: now,
      updatedAt: now,
    };
  } catch (error) {
    const logger = createDatabaseLogger({}, 'transferMVTTokensBetweenUsers', 'UserMVTBalance', { fromUserId, toUserId, amount });
    logError(logger, error, 'transferMVTTokensBetweenUsers', { fromUserId, toUserId, amount });

    if (error.code === 'ResourceNotFoundException') {
      throw new Error("Database tables are not ready yet. Please try again after the schema deployment is complete.");
    }

    throw new Error(error.message || "Failed to transfer MVT tokens between users");
  }
}

/**
 * Enrich transactions with user data
 * @param {Array} transactions - Array of transaction objects
 * @returns {Promise<Array>} - Array of transactions with user data
 */
async function enrichTransactionsWithUserData(transactions) {
  try {
    // Collect all unique user IDs
    const userIds = new Set();

    transactions.forEach(transaction => {
      if (transaction.fromUserId) userIds.add(transaction.fromUserId);
      if (transaction.toUserId) userIds.add(transaction.toUserId);
      if (transaction.adminUserId) userIds.add(transaction.adminUserId);
    });

    // Fetch user data for all unique user IDs
    const userDataMap = await fetchUserDataBatch(Array.from(userIds));

    // Enrich transactions with user data
    return transactions.map(transaction => ({
      ...transaction,
      fromUser: transaction.fromUserId ? userDataMap.get(transaction.fromUserId) : null,
      toUser: transaction.toUserId ? userDataMap.get(transaction.toUserId) : null,
      adminUser: transaction.adminUserId ? userDataMap.get(transaction.adminUserId) : null,
    }));
  } catch (error) {
    const logger = createDatabaseLogger({}, 'enrichTransactionsWithUserData', 'User');
    logError(logger, error, 'enrichTransactionsWithUserData');
    // Return original transactions if enrichment fails
    return transactions;
  }
}

/**
 * Fetch user data for multiple user IDs in batch
 * @param {Array} userIds - Array of user IDs
 * @returns {Promise<Map>} - Map of userId -> user data
 */
async function fetchUserDataBatch(userIds) {
  const userDataMap = new Map();

  if (!userIds || userIds.length === 0) {
    return userDataMap;
  }

  try {
    const userTableName = getTableName("User");

    // Check if table exists
    const exists = await tableExists(ddb, userTableName);
    if (!exists) {
      console.log('User table not found, returning empty user data');
      return userDataMap;
    }

    // Fetch user data in batches (DynamoDB batch get limit is 100)
    const batchSize = 100;
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);

      const requestItems = {};
      requestItems[userTableName] = {
        Keys: batch.map(userId => ({ id: { S: userId } })),
        ProjectionExpression: 'id, givenName, familyName, #name, email',
        ExpressionAttributeNames: {
          '#name': 'name' // 'name' is a reserved keyword in DynamoDB
        }
      };

      const params = { RequestItems: requestItems };
      const result = await ddb.batchGetItem(params).promise();

      if (result?.Responses[userTableName]) {
        result.Responses[userTableName].forEach(item => {
          const user = AWS.DynamoDB.Converter.unmarshall(item);
          userDataMap.set(user.id, {
            id: user.id,
            givenName: user.givenName || '',
            familyName: user.familyName || '',
            name: user.name || '',
            email: user.email || ''
          });
        });
      }
    }
  } catch (error) {
    console.error('Error fetching user data batch:', error);
  }

  return userDataMap;
}

/**
 * Validate and sanitize transaction type
 * @param {string} transactionType - Transaction type to validate
 * @returns {string} - Valid transaction type or default
 */
function validateTransactionType(transactionType) {
  // Valid transaction types from GraphQL schema enum (using constants)
  const validTypes = Object.values(TRANSACTION_TYPES);

  if (!transactionType || typeof transactionType !== 'string') {
    console.warn('Invalid transaction type (null/undefined):', transactionType);
    return TRANSACTION_TYPES.SYSTEM_ADJUSTMENT; // Default fallback
  }

  if (!validTypes.includes(transactionType)) {
    console.warn('Invalid transaction type:', transactionType, 'Valid types:', validTypes);
    return TRANSACTION_TYPES.SYSTEM_ADJUSTMENT; // Default fallback
  }

  return transactionType;
}

/**
 * Map transaction type to display type based on context
 * @param {string} transactionType - Backend transaction type
 * @param {object} transaction - Transaction object
 * @param {string} viewingUserId - ID of user viewing the transaction (null for admin view all)
 * @param {boolean} isAdminViewAll - Whether this is admin viewing all transactions
 * @returns {string} - Display type (SENT, RECEIVED, ADDED)
 */
function mapTransactionDisplayType(transactionType, transaction, viewingUserId, isAdminViewAll) {
  if (!transactionType) return 'ADDED';

  switch (transactionType.toUpperCase()) {
    // Admin minting tokens - always ADDED
    case 'ADMIN_MINT':
    case 'WALLET_MINT':
      return 'ADDED';

    // Central wallet to user transfer - context-aware mapping
    case 'CENTRAL_TO_USER_TRANSFER':
    case 'ADMIN_TRANSFER':
      if (isAdminViewAll) {
        return 'SENT'; // Admin perspective: show as SENT
      }
      return 'RECEIVED'; // User perspective: always RECEIVED

    // User to user transfer - determine based on context
    case 'USER_TO_USER_TRANSFER':
      if (viewingUserId) {
        // Check if viewing user is sender or receiver
        if (transaction.toUserId === viewingUserId) {
          return 'RECEIVED';
        } else if (transaction.fromUserId === viewingUserId) {
          return 'SENT';
        }
      }
      // Default to SENT for admin view or when context is unclear
      return 'SENT';

    // USDC operations
    case 'USDC_DEPOSIT':
      return 'ADDED';
    case 'USDC_WITHDRAWAL':
      return 'SENT';

    // Swap operations - context-aware based on token type and viewing perspective
    case 'SWAP_APPROVED':
      if (transaction.tokenType === TOKEN_TYPES.USDC) {
        // USDC transaction: admin sends USDC to user
        if (isAdminViewAll) {
          return 'SENT'; // Admin perspective: admin sends USDC to user
        } else {
          return 'RECEIVED'; // User perspective: user receives USDC from admin
        }
      } else {
        // MVT transaction: user sends MVT to admin
        if (isAdminViewAll) {
          return 'RECEIVED'; // Admin perspective: admin receives MVT from user
        } else {
          return 'SENT'; // User perspective: user sends MVT to admin
        }
      }
    case 'SWAP_REJECTED':
      return 'SENT'; // Request was rejected, no exchange occurred

    case 'CENTRAL_TO_ORGANIZATION_TRANSFER':
      if (isAdminViewAll) {
        return 'SENT'; // Admin perspective: admin sends to organization
      }
      return 'RECEIVED'; // Organization perspective: organization receives from admin

    // System operations
    case 'REWARD_DISTRIBUTION':
      return 'RECEIVED';
    case 'PENALTY_DEDUCTION':
      return 'SENT';
    case 'SYSTEM_ADJUSTMENT':
      return 'ADDED';

    default:
      console.warn(`Unknown transaction type for display mapping: ${transactionType}`);
      return 'ADDED';
  }
}

/**
 * Validate if a transaction hash is a valid Ethereum transaction hash
 * @param {string} transactionHash - Transaction hash to validate
 * @returns {boolean} - True if valid Ethereum transaction hash
 */
function isValidEthereumTransactionHash(transactionHash) {
  if (!transactionHash || typeof transactionHash !== 'string') {
    return false;
  }

  // Check if it's a valid Ethereum transaction hash format (0x followed by 64 hex characters)
  const ethTxHashRegex = /^0x[a-fA-F0-9]{64}$/;

  // Exclude internal transaction IDs
  if (transactionHash.startsWith('internal-') ||
      transactionHash.startsWith('onramp-') ||
      transactionHash.startsWith('usdc-') ||
      transactionHash.startsWith('mvt-')) {
    return false;
  }

  return ethTxHashRegex.test(transactionHash);
}

/**
 * Generate display-ready transaction details
 * @param {object} transaction - Transaction object with user data
 * @param {string} displayType - Display type (SENT, RECEIVED, ADDED)
 * @returns {object} - Display details object
 */
function generateTransactionDisplayDetails(transaction, displayType) {
  const details = {
    displayType: displayType,
    primaryLabel: '',
    secondaryInfo: '',
    showEtherscanLink: false
  };

  // Determine primary label based on display type and transaction details
  switch (displayType) {
    case 'SENT':
      // Check for organization transfers first
      if (transaction.transactionType === TRANSACTION_TYPES.CENTRAL_TO_ORGANIZATION_TRANSFER) {
        if (transaction.toOrganization && transaction.toOrganization.name) {
          details.primaryLabel = `To: ${transaction.toOrganization.name}`;
        } else {
          details.primaryLabel = 'To: Organization';
        }
      } else if (transaction.toUser && (transaction.toUser.givenName || transaction.toUser.familyName)) {
        const toName = `${transaction.toUser.givenName || ''} ${transaction.toUser.familyName || ''}`.trim();
        details.primaryLabel = `To: ${toName}`;
      } else if (transaction.toUserId === 'central-mvt-wallet' || transaction.toWalletId === 'central-mvt-wallet') {
        details.primaryLabel = 'To: MY Village Wallet';
      } else {
        details.primaryLabel = 'To: Unknown';
      }
      break;

    case 'RECEIVED':
      // Check for organization transfers first
      if (transaction.transactionType === TRANSACTION_TYPES.ORGANIZATION_TO_USER_TRANSFER) {
        details.primaryLabel = 'From: Organization';
      } else if (transaction.transactionType === TRANSACTION_TYPES.CENTRAL_TO_ORGANIZATION_TRANSFER) {
        details.primaryLabel = 'From: MY Village Wallet';
      } else if (transaction.fromUser && (transaction.fromUser.givenName || transaction.fromUser.familyName)) {
        const fromName = `${transaction.fromUser.givenName || ''} ${transaction.fromUser.familyName || ''}`.trim();
        details.primaryLabel = `From: ${fromName}`;
      } else if (transaction.fromUserId === 'central-mvt-wallet' || transaction.fromWalletId === 'central-mvt-wallet') {
        details.primaryLabel = 'From: MY Village Wallet';
      } else if (transaction.transactionType === 'ADMIN_MINT') {
        details.primaryLabel = 'From: Admin';
      } else {
        details.primaryLabel = 'From: Unknown';
      }
      break;

    case 'ADDED':
      if (transaction.transactionType === 'ADMIN_MINT') {
        details.primaryLabel = 'From: Admin';
      } else if (transaction.fromUser && (transaction.fromUser.givenName || transaction.fromUser.familyName)) {
        const fromName = `${transaction.fromUser.givenName || ''} ${transaction.fromUser.familyName || ''}`.trim();
        details.primaryLabel = `From: ${fromName}`;
      } else {
        details.primaryLabel = 'From: System';
      }
      break;
  }

  // Set secondary info and Etherscan link visibility based on token type and transaction hash
  if (transaction.tokenType === TOKEN_TYPES.USDC) {
    details.showEtherscanLink = isValidEthereumTransactionHash(transaction.transactionHash);
    details.secondaryInfo = 'USDC Transaction';

    // Special handling for swap transactions
    if (transaction.transactionType === TRANSACTION_TYPES.SWAP_APPROVED) {
      details.secondaryInfo = 'Swap: USDC Received';
    }
  } else {
    details.showEtherscanLink = isValidEthereumTransactionHash(transaction.transactionHash);
    details.secondaryInfo = 'MVT Transaction';

    // Special handling for organization transactions
    if (transaction.transactionType === TRANSACTION_TYPES.USER_TO_ORGANIZATION_TRANSFER) {
      details.secondaryInfo = 'Organization Transfer';
    } else if (transaction.transactionType === TRANSACTION_TYPES.ORGANIZATION_TO_USER_TRANSFER) {
      details.secondaryInfo = 'Organization Transfer';
    } else if (transaction.transactionType === TRANSACTION_TYPES.CENTRAL_TO_ORGANIZATION_TRANSFER) {
      details.secondaryInfo = 'Activity Reward';
    } else if (transaction.transactionType === TRANSACTION_TYPES.SWAP_APPROVED) {
      details.secondaryInfo = 'Swap: MVT Exchanged';
    } else if (transaction.transactionType === TRANSACTION_TYPES.SWAP_REJECTED) {
      details.secondaryInfo = 'Swap: Request Rejected';
    } 
  }

  return details;
}

/**
 * Get MVT Wallet Transaction List
 * @param {string} address - User address or wallet ID
 * @param {boolean} isAdmin - Whether the request is from an admin
 * @param {number} limit - Maximum number of transactions to return
 * @param {string} organizationId - Optional organization ID to filter organization transactions
 * @returns {Promise<Array>} - Array of transactions
 */
async function getMVTWalletTransactionList(address, isAdmin = false, limit = 50, organizationId = null) {
  const requestId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
  
  // Only log organization-related debug info if organizationId is provided
  if (organizationId) {
    console.log(`[${requestId}] Starting transaction list fetch for organization: ${organizationId}`);
  }
  
  try {
    const transactionTableName = getTableName("MVTWalletTransaction");
    // Check if table exists first
    const exists = await tableExists(ddb, transactionTableName);
    if (!exists) {
      console.warn(`[${requestId}] Table ${transactionTableName} does not exist`);
      return [];
    }

    // To ensure proper sorting, we need to fetch more records than the requested limit
    const fetchLimit = Math.max(limit * 10, 200); // Fetch at least 10x the limit or 200, whichever is higher

    let params;

    if (isAdmin && (address === "all" || address === CENTRAL_WALLET_ID || !address)) {
      // Admin viewing all transactions - include all transaction types
      params = {
        TableName: transactionTableName,
        Limit: fetchLimit,
      };
    } else if (isAdmin) {
      // Admin viewing specific user's or organization's transactions
      params = {
        TableName: transactionTableName,
        FilterExpression: "fromUserId = :userId OR toUserId = :userId OR organizationId = :userId OR memberId = :userId",
        ExpressionAttributeValues: {
          ":userId": { S: address }
        },
        Limit: fetchLimit,
      };
    } else if (organizationId) {
      // Organization view
      params = {
        TableName: transactionTableName,
        FilterExpression: "organizationId = :orgId OR memberId = :orgId",
        ExpressionAttributeValues: {
          ":orgId": { S: organizationId }
        },
        Limit: fetchLimit,
      };
    } else {
      // Regular user view - only show their own transactions
      params = {
        TableName: transactionTableName,
        FilterExpression: "fromUserId = :userId OR toUserId = :userId",
        ExpressionAttributeValues: {
          ":userId": { S: address }
        },
        Limit: fetchLimit,
      };
    }

    const result = await ddb.scan(params).promise();
    const allTransactions = result.Items.map(item => AWS.DynamoDB.Converter.unmarshall(item));
    
    // Log basic info for debugging
    if (allTransactions.length >= fetchLimit) {
      console.warn(`[${requestId}] Reached fetch limit of ${fetchLimit} transactions. Some transactions may be missing.`);
    }

    const transactions = result.Items
      .map((item) => {
        const transaction = AWS.DynamoDB.Converter.unmarshall(item);

        // Validate and sanitize transaction type to prevent GraphQL serialization errors
        const validatedTransactionType = validateTransactionType(transaction.transactionType);
        
        // Transaction type validation is complete

        return {
          id: transaction.id,
          transactionType: validatedTransactionType,
          tokenType: transaction.tokenType || TOKEN_TYPES.MVT, // Default to MVT for backward compatibility
          amount: transaction.amount,
          fromWalletId: transaction.fromWalletId,
          toWalletId: transaction.toWalletId,
          fromUserId: transaction.fromUserId,
          toUserId: transaction.toUserId,
          status: transaction.status,
          transactionHash: transaction.transactionHash,
          internalTxId: transaction.internalTxId,
          description: transaction.description,
          adminUserId: transaction.adminUserId,
          gasUsed: transaction.gasUsed,
          gasPrice: transaction.gasPrice,
          blockNumber: transaction.blockNumber,
          confirmations: transaction.confirmations,
          metadata: transaction.metadata,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
        };
      })
    

    // Fetch user details for all transactions
    console.log(`[${requestId}] Enriching ${transactions.length} transactions with user data`);
    const enrichedTransactions = await enrichTransactionsWithUserData(transactions);
    console.log(`[${requestId}] Enriched ${enrichedTransactions.length} transactions`);
    
    if (enrichedTransactions.length > 0) {
      console.log(`[${requestId}] First enriched transaction:`, 
        JSON.stringify(enrichedTransactions[0], null, 2));
    }

    // Determine viewing context for display type mapping
    const isAdminViewAll = isAdmin && (address === CENTRAL_WALLET_ID || address === "all" || !address);
    const viewingUserId = isAdminViewAll ? null : address;
    console.log(`[${requestId}] Viewing context - isAdminViewAll: ${isAdminViewAll}, viewingUserId: ${viewingUserId}`);

    // Add display-ready data to each transaction
    console.log(`[${requestId}] Adding display-ready data to transactions`);
    const displayReadyTransactions = enrichedTransactions.map((transaction, index) => {
      // Map transaction type to display type based on context
      const displayType = mapTransactionDisplayType(
        transaction.transactionType,
        transaction,
        viewingUserId,
        isAdminViewAll
      );

      // Generate display details
      const displayDetails = generateTransactionDisplayDetails(transaction, displayType);

      return {
        ...transaction,
        // Add display-ready fields
        displayType: displayDetails.displayType,
        primaryLabel: displayDetails.primaryLabel,
        secondaryInfo: displayDetails.secondaryInfo,
        showEtherscanLink: displayDetails.showEtherscanLink,
        // Add formatted date for frontend convenience
        formattedDate: new Date(transaction.createdAt).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'UTC'
        })
      };
    });

    // Sort by creation date (most recent first)
    displayReadyTransactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Apply the requested limit after sorting to get the most recent transactions
    const finalTransactions = displayReadyTransactions.slice(0, limit);
    
    // Only log organization transaction summary if organizationId was provided
    if (organizationId) {
      const transactionTypes = finalTransactions.reduce((acc, tx) => {
        acc[tx.transactionType] = (acc[tx.transactionType] || 0) + 1;
        return acc;
      }, {});
      
      console.log(`[${requestId}] Found ${finalTransactions.length} transactions for organization ${organizationId}:`, 
        JSON.stringify(transactionTypes, null, 2));
    }
    
    return finalTransactions;
  } catch (error) {
    const errorContext = {
      message: error.message,
      code: error.code,
      organizationId: organizationId || 'N/A'
    };
    
    if (organizationId) {
      console.error(`[${requestId}] Error fetching organization transactions:`, errorContext);
    }

    if (error.code === 'ResourceNotFoundException') {
      console.error(`[${requestId}] MVTWalletTransaction table not found`);
      return [];
    }

    throw error;
  }
}

/**
      scanParams: debug.scanParams,
      rawItems: debug.rawItems,
      organizationTransactions: debug.organizationTransactions
    });

    if (error.code === 'ResourceNotFoundException') {
      console.log('MVTWalletTransaction table not found, returning empty array');
      return [];
    }

    throw error;
  }
}

/**
 * Get MVT Wallet Transaction by ID
 * @param {string} transactionId - Transaction ID
 * @param {string} currentUserId - Current user ID for authorization
 * @param {object} event - GraphQL event for admin check
 * @returns {Promise<object|null>} - Transaction object or null if not found/unauthorized
 */
async function getMVTWalletTransactionById(transactionId, currentUserId, event) {
  try {
    const transactionTableName = getTableName("MVTWalletTransaction");

    // Check if table exists first
    const exists = await tableExists(ddb, transactionTableName);
    if (!exists) {
      return null;
    }

    const params = {
      TableName: transactionTableName,
      Key: {
        id: { S: transactionId },
      },
    };

    const result = await ddb.getItem(params).promise();

    if (!result.Item) {
      return null;
    }

    const transaction = AWS.DynamoDB.Converter.unmarshall(result.Item);

    // Check authorization - users can only see their own transactions unless they're admin
    const authService = require('../../shared/services/authService');
    const isAdmin = await authService.checkAdminAuthorization(event);
    if (!isAdmin) {
      const isUserTransaction =
        transaction.fromUserId === currentUserId ||
        transaction.toUserId === currentUserId;

      if (!isUserTransaction) {
        return null;
      }
    }

    // Validate and sanitize transaction type to prevent GraphQL serialization errors
    const validatedTransactionType = validateTransactionType(transaction.transactionType);

    return {
      id: transaction.id,
      transactionType: validatedTransactionType,
      tokenType: transaction.tokenType || TOKEN_TYPES.MVT, // Default to MVT for backward compatibility
      amount: transaction.amount,
      fromWalletId: transaction.fromWalletId,
      toWalletId: transaction.toWalletId,
      fromUserId: transaction.fromUserId,
      toUserId: transaction.toUserId,
      status: transaction.status,
      transactionHash: transaction.transactionHash,
      internalTxId: transaction.internalTxId,
      description: transaction.description,
      adminUserId: transaction.adminUserId,
      gasUsed: transaction.gasUsed,
      gasPrice: transaction.gasPrice,
      blockNumber: transaction.blockNumber,
      confirmations: transaction.confirmations,
      metadata: transaction.metadata,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };
  } catch (error) {
    console.error("Error getting MVT wallet transaction by ID:", error);
    return null;
  }
}

/**
 * Update MVT Wallet Transaction Status
 * @param {string} transactionId - Transaction ID to update
 * @param {string} newStatus - New status for the transaction
 * @param {string} description - Optional updated description
 * @returns {Promise<boolean>} - True if successful, false if failed
 */
async function updateMVTWalletTransactionStatus(transactionId, newStatus, description = null) {
  try {
    const transactionTableName = getTableName("MVTWalletTransaction");

    // Check if table exists first
    const exists = await tableExists(ddb, transactionTableName);
    if (!exists) {
      console.log('MVTWalletTransaction table not found, skipping transaction status update');
      return false;
    }

    const now = new Date().toISOString();

    let updateExpression = "SET #status = :status, updatedAt = :updatedAt";
    let expressionAttributeNames = { "#status": "status" };
    let expressionAttributeValues = {
      ":status": { S: newStatus },
      ":updatedAt": { S: now }
    };

    if (description) {
      updateExpression += ", description = :description";
      expressionAttributeValues[":description"] = { S: description };
    }

    const updateParams = {
      TableName: transactionTableName,
      Key: {
        id: { S: transactionId }
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ConditionExpression: "attribute_exists(id)"
    };

    await ddb.updateItem(updateParams).promise();
    return true;
  } catch (error) {
    if (error.code === 'ConditionalCheckFailedException') {
      console.warn(`Transaction ${transactionId} not found for status update`);
    } else {
      console.error(`Error updating transaction ${transactionId} status:`, error);
    }
    return false;
  }
}

/**
 * Get transactions by metadata criteria
 * @param {object} metadataFilter - Metadata filter criteria
 * @returns {Promise<Array>} - Array of matching transactions
 */
async function getTransactionsByMetadata(metadataFilter) {
  try {
    const transactionTableName = getTableName("MVTWalletTransaction");

    // Check if table exists first
    const exists = await tableExists(ddb, transactionTableName);
    if (!exists) {
      return [];
    }

    // Build filter expression for metadata search
    let filterExpression = 'isDeleted = :isDeleted';
    const expressionAttributeValues = {
      ':isDeleted': { S: 'false' }
    };

    // Add metadata filters
    if (metadataFilter) {
      Object.keys(metadataFilter).forEach((key, index) => {
        const valueKey = `:metadataValue${index}`;
        filterExpression += ` AND contains(metadata, ${valueKey})`;
        expressionAttributeValues[valueKey] = { S: `"${key}":"${metadataFilter[key]}"` };
      });
    }

    const params = {
      TableName: transactionTableName,
      FilterExpression: filterExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      Limit: 10 // Limit to prevent large scans
    };

    const result = await ddb.scan(params).promise();

    if (!result.Items || result.Items.length === 0) {
      return [];
    }

    // Convert DynamoDB items to JavaScript objects
    const transactions = result.Items.map(item => {
      const transaction = AWS.DynamoDB.Converter.unmarshall(item);

      // Parse metadata if it's a string
      if (typeof transaction.metadata === 'string') {
        try {
          transaction.metadata = JSON.parse(transaction.metadata);
        } catch (e) {
          console.warn('Failed to parse metadata for transaction:', transaction.id);
          transaction.metadata = {};
        }
      }

      return transaction;
    });

    // Additional filtering on parsed metadata for exact matches
    const filteredTransactions = transactions.filter(transaction => {
      if (!metadataFilter || !transaction.metadata) return true;

      return Object.keys(metadataFilter).every(key =>
        transaction.metadata[key] === metadataFilter[key]
      );
    });

    return filteredTransactions;
  } catch (error) {
    console.error("Error getting transactions by metadata:", error);

    if (error.code === 'ResourceNotFoundException') {
      console.log('MVTWalletTransaction table not found, returning empty array');
      return [];
    }

    throw error;
  }
}

module.exports = {
  generateTransactionId,
  createMVTWalletTransaction,
  updateMVTWalletTransactionStatus,
  validateTransactionType,
  validateWalletId,
  mapTransactionDisplayType,
  generateTransactionDisplayDetails,
  isValidEthereumTransactionHash,
  mintMVTTokens,
  transferMVTToUser,
  transferMVTBetweenUsers,
  enrichTransactionsWithUserData,
  fetchUserDataBatch,
  getMVTWalletTransactionList,

  getMVTWalletTransactionById,
  getTransactionsByMetadata
};
